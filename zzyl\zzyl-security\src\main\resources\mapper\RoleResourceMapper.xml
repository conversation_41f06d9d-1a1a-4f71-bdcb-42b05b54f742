<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zzyl.mapper.RoleResourceMapper">
    <resultMap id="BaseResultMap" type="com.zzyl.entity.RoleResource">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="role_id" jdbcType="BIGINT" property="roleId"/>
        <result column="resource_no" jdbcType="VARCHAR" property="resourceNo"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="data_state" jdbcType="CHAR" property="dataState"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , role_id, resource_no, create_time, update_time, data_state, create_by, update_by
    </sql>

    <delete id="deleteRoleResourceByRoleId" parameterType="java.lang.Long">
        delete
        from sys_role_resource
        where role_id = #{roleId,jdbcType=BIGINT}
    </delete>
    <delete id="deleteRoleResourceInRoleId" parameterType="java.util.List">
        delete from sys_role_resource
        where role_id in
        <foreach close=")" collection="roleIds" item="roleId" open="(" separator=",">
            #{roleId}
        </foreach>
    </delete>
    <select id="checkMenuExistRole" resultType="Integer">
        select count(1)
        from sys_role_resource
        where resource_no = #{menuId}
    </select>

    <insert id="insertSelective" parameterType="com.zzyl.entity.RoleResource">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into sys_role_resource
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roleId != null">
                role_id,
            </if>
            <if test="resourceNo != null">
                resource_no,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="dataState != null">
                data_state,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roleId != null">
                #{roleId,jdbcType=BIGINT},
            </if>
            <if test="resourceNo != null">
                #{resourceNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dataState != null">
                #{dataState,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zzyl.entity.RoleResource">
        update sys_role_resource
        <set>
            <if test="roleId != null">
                role_id = #{roleId,jdbcType=BIGINT},
            </if>
            <if test="resourceNo != null">
                resource_no = #{resourceNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dataState != null">
                data_state = #{dataState,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="java.util.List" useGeneratedKeys="true">
        insert into sys_role_resource (role_id, resource_no, create_time, update_time, data_state, create_by, update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.roleId,jdbcType=BIGINT}, #{item.resourceNo,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.dataState,jdbcType=CHAR}, #{item.createBy,jdbcType=BIGINT},
            #{item.updateBy,jdbcType=BIGINT})
        </foreach>
    </insert>
</mapper>
