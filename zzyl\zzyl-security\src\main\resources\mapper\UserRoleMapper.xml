<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zzyl.mapper.UserRoleMapper">
    <resultMap id="BaseResultMap" type="com.zzyl.entity.UserRole">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="role_id" jdbcType="BIGINT" property="roleId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="data_state" jdbcType="CHAR" property="dataState"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , user_id, role_id, create_time, update_time, data_state, create_by, update_by
    </sql>

    <delete id="deleteUserRoleByUserId" parameterType="java.lang.Long">
        delete
        from sys_user_role
        where user_id = #{userId}
    </delete>
    <delete id="deleteUserRoleInUserId" parameterType="java.util.List">
        delete from sys_user_role
        where user_id in(
        <foreach collection='userIds' separator=',' item='userId'>
            #{userId}
        </foreach>)
    </delete>

    <insert id="insertSelective" parameterType="com.zzyl.entity.UserRole">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into sys_user_role
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="roleId != null">
                role_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="dataState != null">
                data_state,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="roleId != null">
                #{roleId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dataState != null">
                #{dataState,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zzyl.entity.UserRole">
        update sys_user_role
        <set>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="roleId != null">
                role_id = #{roleId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="dataState != null">
                data_state = #{dataState,jdbcType=CHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="java.util.List" useGeneratedKeys="true">
        insert into sys_user_role (user_id, role_id, create_time, update_time, data_state, create_by, update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.userId,jdbcType=BIGINT}, #{item.roleId,jdbcType=BIGINT}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.dataState,jdbcType=CHAR}, #{item.createBy,jdbcType=BIGINT},
            #{item.updateBy,jdbcType=BIGINT})
        </foreach>
    </insert>

    <select id="countUserRoleByRoleId" resultType="Integer">
        select count(1)
        from sys_user_role
        where role_id = #{roleId}
    </select>
</mapper>
