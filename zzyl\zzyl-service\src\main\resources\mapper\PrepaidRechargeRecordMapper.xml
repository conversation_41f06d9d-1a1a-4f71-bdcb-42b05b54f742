<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zzyl.mapper.PrepaidRechargeRecordMapper">
    <resultMap id="BaseResultMap" type="com.zzyl.entity.PrepaidRechargeRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="recharge_amount" jdbcType="DECIMAL" property="rechargeAmount"/>
        <result column="recharge_voucher" jdbcType="VARCHAR" property="rechargeVoucher"/>
        <result column="recharge_method" jdbcType="VARCHAR" property="rechargeMethod"/>
        <result column="elder_id" jdbcType="BIGINT" property="elderId"/>
        <result column="elder_name" jdbcType="VARCHAR" property="elderName"/>
        <result column="prepaid_recharge_no" jdbcType="VARCHAR" property="prepaidRechargeNo"/>
        <result column="bed_no" jdbcType="VARCHAR" property="bedNo"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>
    <sql id="Base_Column_List">
        p.id
        , p.recharge_amount, p.recharge_voucher, p.recharge_method, p.elder_id,
    p.create_time, p.update_time, p.create_by, p.update_by, p.remark, p.prepaid_recharge_no
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from prepaid_recharge_record p
        where p.id = #{id,jdbcType=BIGINT}
    </select>
    <select id="prepaidRechargeRecordPage" resultType="com.zzyl.entity.PrepaidRechargeRecord">
        select
        <include refid="Base_Column_List"/>
        , e.name as elder_name
        ,s.real_name as creator
        , cc.bed_no as bed_no
        from prepaid_recharge_record p
        left join elder e on p.elder_id = e.id
        left join check_in_config cc on cc.elder_id = e.id
        LEFT JOIN sys_user s ON p.create_by = s.id
        <where>
            <if test="bedNo != null ">
                AND cc.bed_no = #{bedNo}
            </if>
            <if test="elderName != null ">
                AND e.name = #{elderName}
            </if>
        </where>
        order by p.create_time desc
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from prepaid_recharge_record
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zzyl.entity.PrepaidRechargeRecord">
        insert into prepaid_recharge_record (id, recharge_amount, recharge_voucher,
                                             recharge_method, elder_id, create_time, update_time,
                                             create_by, update_by, remark, prepaid_recharge_no)
        values (#{id,jdbcType=BIGINT}, #{rechargeAmount,jdbcType=DECIMAL}, #{rechargeVoucher,jdbcType=VARCHAR},
                #{rechargeMethod,jdbcType=VARCHAR}, #{elderId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
                #{updateTime,jdbcType=TIMESTAMP},
                #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{remark,jdbcType=VARCHAR},
                #{prepaidRechargeNo,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.zzyl.entity.PrepaidRechargeRecord">
        insert into prepaid_recharge_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="rechargeAmount != null">
                recharge_amount,
            </if>
            <if test="rechargeVoucher != null">
                recharge_voucher,
            </if>
            <if test="rechargeMethod != null">
                recharge_method,
            </if>
            <if test="elderId != null">
                elder_id,
            </if>

            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="rechargeAmount != null">
                #{rechargeAmount,jdbcType=DECIMAL},
            </if>
            <if test="rechargeVoucher != null">
                #{rechargeVoucher,jdbcType=VARCHAR},
            </if>
            <if test="rechargeMethod != null">
                #{rechargeMethod,jdbcType=VARCHAR},
            </if>
            <if test="elderId != null">
                #{elderId,jdbcType=BIGINT},
            </if>

            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zzyl.entity.PrepaidRechargeRecord">
        update prepaid_recharge_record
        <set>
            <if test="rechargeAmount != null">
                recharge_amount = #{rechargeAmount,jdbcType=DECIMAL},
            </if>
            <if test="rechargeVoucher != null">
                recharge_voucher = #{rechargeVoucher,jdbcType=VARCHAR},
            </if>
            <if test="rechargeMethod != null">
                recharge_method = #{rechargeMethod,jdbcType=VARCHAR},
            </if>
            <if test="elderId != null">
                elder_id = #{elderId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zzyl.entity.PrepaidRechargeRecord">
        update prepaid_recharge_record
        set recharge_amount  = #{rechargeAmount,jdbcType=DECIMAL},
            recharge_voucher = #{rechargeVoucher,jdbcType=VARCHAR},
            recharge_method  = #{rechargeMethod,jdbcType=VARCHAR},
            elder_id         = #{elderId,jdbcType=BIGINT},
            create_time      = #{createTime,jdbcType=TIMESTAMP},
            update_time      = #{updateTime,jdbcType=TIMESTAMP},
            create_by        = #{createBy,jdbcType=BIGINT},
            update_by        = #{updateBy,jdbcType=BIGINT},
            remark           = #{remark,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>
