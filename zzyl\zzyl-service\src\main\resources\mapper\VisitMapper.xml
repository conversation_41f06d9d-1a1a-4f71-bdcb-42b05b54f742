<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.zzyl.mapper.VisitMapper">
    <resultMap id="VisitMap" type="com.zzyl.entity.Visit">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="mobile" property="mobile"/>
        <result column="time" property="time"/>
        <result column="visitor" property="visitor"/>
        <result column="type" property="type"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="creator" property="creator"/>
    </resultMap>

    <insert id="insert" parameterType="com.zzyl.entity.Visit" useGeneratedKeys="true" keyProperty="id">
        insert into visit(name, mobile, time, visitor, type, status, create_by, remark, create_time)
        values (#{name}, #{mobile}, #{time}, #{visitor}, #{type}, #{status}, #{createBy}, #{remark}, #{createTime})
    </insert>

    <update id="update" parameterType="com.zzyl.entity.Visit">
        update visit
        set name        = #{name},
            mobile      = #{mobile},
            time        = #{time},
            visitor     = #{visitor},
            type        = #{type},
            status      = #{status},
            update_time = #{updateTime},
            update_by   = #{updateBy}
        where id = #{id}
    </update>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete
        from visit
        where id = #{id}
    </delete>

    <select id="findById" parameterType="java.lang.Long" resultType="com.zzyl.entity.Visit">
        select *
        from visit
        where id = #{id}
    </select>

    <select id="findAll" resultMap="VisitMap">
        select * from visit
        WHERE time BETWEEN #{startTime} AND #{endTime}
        <if test="mobile != null and mobile != ''">
            and mobile = #{mobile}
        </if>
        order by create_time desc
    </select>

    <select id="findByPage" parameterType="java.util.Map" resultType="com.zzyl.entity.Visit">
        select v.name, v.mobile, v.time, v.visitor, v.type, v.status, v.create_by, v.remark, v.create_time, s.real_name
        as creator from visit v
        LEFT JOIN sys_user s ON v.create_by = s.id
        <where>
            <if test="name != null and name != ''">
                and v.name like concat('%',#{name},'%')
            </if>
            <if test="mobile != null and mobile != ''">
                and v.mobile = #{mobile}
            </if>
            <if test="status != null">
                and v.status = #{status, jdbcType=INTEGER}
            </if>
            <if test="type != null">
                and v.type = #{type, jdbcType=INTEGER}
            </if>
            <if test="createBy != null">
                AND v.create_by = #{createBy}
            </if>
            <if test="startTime != null and endTime != null">
                AND v.time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>
