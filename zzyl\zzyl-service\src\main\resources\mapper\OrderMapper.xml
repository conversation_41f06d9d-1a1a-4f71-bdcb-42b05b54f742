<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zzyl.mapper.OrderMapper">
    <resultMap id="BaseResultMap" type="com.zzyl.entity.Order">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="trading_order_no" jdbcType="BIGINT" property="tradingOrderNo"/>
        <result column="payment_status" jdbcType="TINYINT" property="paymentStatus"/>
        <result column="amount" jdbcType="DECIMAL" property="amount"/>
        <result column="refund" jdbcType="DECIMAL" property="refund"/>
        <result column="is_refund" jdbcType="VARCHAR" property="isRefund"/>
        <result column="member_id" jdbcType="BIGINT" property="memberId"/>
        <result column="project_id" jdbcType="BIGINT" property="projectId"/>
        <result column="elder_id" jdbcType="BIGINT" property="elderId"/>
        <result column="estimated_arrival_time" jdbcType="TIMESTAMP" property="estimatedArrivalTime"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="reason" jdbcType="VARCHAR" property="reason"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        <result column="creator" property="creator"/>
        <result column="updater" property="updater"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="o_create_type" property="createType"/>
        <collection property="elderVo" ofType="com.zzyl.vo.retreat.ElderVo">
            <id column="id" jdbcType="BIGINT" property="id"/>
            <result column="ename" jdbcType="VARCHAR" property="name"/>
            <result column="image" jdbcType="VARCHAR" property="image"/>
            <result column="id_card_no" jdbcType="VARCHAR" property="idCardNo"/>
            <result column="status" jdbcType="INTEGER" property="status"/>
            <result column="create_by" jdbcType="BIGINT" property="createBy"/>
            <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        </collection>
        <collection property="nursingProjectVo" ofType="com.zzyl.vo.NursingProjectVo">
            <id column="id" property="id"/>
            <result column="pname" property="name"/>
            <result column="unit" property="unit"/>
            <result column="price" property="price"/>
            <result column="pimage" property="image"/>
            <result column="nursing_requirement" property="nursingRequirement"/>
            <result column="status" property="status"/>
            <result column="create_by" property="createBy"/>
            <result column="update_by" property="updateBy"/>
            <result column="creator" property="creator"/>
        </collection>
        <collection property="bedVo" ofType="com.zzyl.vo.BedVo">
            <id column="id" property="id"/>
            <result column="bed_number" property="bedNumber"/>
            <result column="bed_status" property="bedStatus"/>
            <result column="room_id" property="roomId"/>
            <result property="sort" column="sort"/>
            <result column="create_by" property="createBy"/>
            <result column="update_by" property="updateBy"/>
        </collection>
        <collection property="memberVo" ofType="com.zzyl.vo.MemberVo">
            <id column="id" property="id"/>
            <result column="auth_id" property="authId"/>
            <result column="id_card_no" property="idCardNo"/>
            <result column="id_card_no_verify" property="idCardNoVerify"/>
            <result column="phone" property="phone"/>
            <result column="mname" property="name"/>
            <result column="avatar" property="avatar"/>
            <result column="open_id" property="openId"/>
            <result column="sex" property="sex"/>
            <result column="birthday" property="birthday"/>
            <result column="create_by" property="createBy"/>
            <result column="update_by" property="updateBy"/>
        </collection>

        <collection property="tradingVo" ofType="com.zzyl.vo.TradingVo">
            <id column="id" jdbcType="BIGINT" property="id"/>
            <result column="product_order_no" jdbcType="BIGINT" property="productOrderNo"/>
            <result column="trading_order_no" jdbcType="BIGINT" property="tradingOrderNo"/>
            <result column="trading_channel" jdbcType="VARCHAR" property="tradingChannel"/>
            <result column="trading_state" jdbcType="INTEGER" property="tradingState"/>
            <result column="payee_name" jdbcType="VARCHAR" property="payeeName"/>
            <result column="payee_id" jdbcType="BIGINT" property="payeeId"/>
            <result column="payer_name" jdbcType="VARCHAR" property="payerName"/>
            <result column="payer_id" jdbcType="BIGINT" property="payerId"/>
            <result column="trading_amount" jdbcType="DECIMAL" property="tradingAmount"/>
            <result column="refund" jdbcType="DECIMAL" property="refund"/>
            <result column="is_refund" jdbcType="VARCHAR" property="isRefund"/>
            <result column="result_code" jdbcType="VARCHAR" property="resultCode"/>
            <result column="result_msg" jdbcType="VARCHAR" property="resultMsg"/>
            <result column="result_json" jdbcType="VARCHAR" property="resultJson"/>
            <result column="place_order_code" jdbcType="VARCHAR" property="placeOrderCode"/>
            <result column="place_order_msg" jdbcType="VARCHAR" property="placeOrderMsg"/>
            <result column="place_order_json" jdbcType="VARCHAR" property="placeOrderJson"/>
            <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId"/>
            <result column="open_id" jdbcType="VARCHAR" property="openId"/>
            <result column="tcreate_time" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="tupdate_time" jdbcType="TIMESTAMP" property="updateTime"/>
            <result column="create_by" jdbcType="BIGINT" property="createBy"/>
            <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
        </collection>

        <collection property="refundRecordVo" ofType="com.zzyl.vo.RefundRecordVo">
            <id column="id" jdbcType="BIGINT" property="id"/>
            <result column="trading_order_no" jdbcType="BIGINT" property="tradingOrderNo"/>
            <result column="product_order_no" jdbcType="BIGINT" property="productOrderNo"/>
            <result column="refund_no" jdbcType="BIGINT" property="refundNo"/>
            <result column="enterprise_id" jdbcType="BIGINT" property="enterpriseId"/>
            <result column="trading_channel" jdbcType="VARCHAR" property="tradingChannel"/>
            <result column="refund_status" jdbcType="INTEGER" property="refundStatus"/>
            <result column="refund_code" jdbcType="VARCHAR" property="refundCode"/>
            <result column="refund_msg" jdbcType="VARCHAR" property="refundMsg"/>
            <result column="rrmemo" jdbcType="VARCHAR" property="memo"/>
            <result column="refund_amount" jdbcType="DECIMAL" property="refundAmount"/>
            <result column="rrcreate_time" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="rrupdate_time" jdbcType="TIMESTAMP" property="updateTime"/>
            <result column="rrcreate_by" jdbcType="BIGINT" property="createBy"/>
            <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
            <result column="admin_creator" property="adminCreator"/>
            <result column="member_creator" property="creator"/>
            <result column="create_type" property="createType"/>
        </collection>

        <collection property="nursingTaskVo" ofType="com.zzyl.vo.NursingTaskVo">
            <id column="id" jdbcType="BIGINT" property="id"/>
            <result column="nursing_id" jdbcType="BIGINT" property="nursingId"/>
            <result column="project_id" jdbcType="INTEGER" property="projectId"/>
            <result column="elder_id" jdbcType="BIGINT" property="elderId"/>
            <result column="bed_number" jdbcType="VARCHAR" property="bedNumber"/>
            <result column="task_type" jdbcType="TINYINT" property="taskType"/>
            <result column="estimated_server_time" jdbcType="TIMESTAMP" property="estimatedServerTime"/>
            <result column="amark" jdbcType="VARCHAR" property="mark"/>
            <result column="cancel_reason" jdbcType="VARCHAR" property="cancelReason"/>
            <result column="status" jdbcType="INTEGER" property="status"/>
            <result column="rel_no" jdbcType="VARCHAR" property="relNo"/>
            <result column="task_image" jdbcType="VARCHAR" property="taskImage"/>
            <result column="acreate_time" jdbcType="TIMESTAMP" property="createTime"/>
            <result column="aupdate_time" jdbcType="TIMESTAMP" property="updateTime"/>
            <result column="create_by" jdbcType="BIGINT" property="createBy"/>
            <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
            <result column="remark" jdbcType="VARCHAR" property="remark"/>
            <result column="acreator" jdbcType="VARCHAR" property="creator"/>
        </collection>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , trading_order_no, payment_status, amount, refund, is_refund, member_id, project_id,
    elder_id, estimated_arrival_time, order_no, reason, status, create_time, update_time,
    create_by, update_by, remark
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT o.*,
               m.name           as mname,
               m.phone,
               e.name           as ename,
               p.name           as pname,
               p.image          as pimage,
               p.unit           as unit,
               bed.*,
               t.id,
               t.product_order_no,
               t.trading_order_no,
               t.trading_channel,
               t.trading_type,
               t.trading_state,
               t.payee_name,
               t.payee_id,
               t.payer_name,
               t.payer_id,
               t.trading_amount,
               t.refund,
               t.is_refund,
               t.result_code,
               t.result_msg,
               t.result_json,
               t.place_order_code,
               t.place_order_msg,
               t.enterprise_id,
               t.memo,
               t.open_id,
               t.enable_flag,
               t.create_by,
               t.update_by,
               t.remark,
               t.place_order_json,
               t.qr_code,
               t.create_time    as tcreate_time,
               t.update_time    as tupdate_time,
               rr.memo          as rrmemo,
               rr.refund_status as refund_status,
               rr.create_by     as rrcreate_by,
               rr.refund_code,
               rr.refund_no,
               rr.refund_amount,
               rr.refund_msg,
               rr.refund_code,
               rr.refund_msg,
               rr.create_time   as rrcreate_time,
               rr.update_time   as rrupdate_time,
               rr.create_type   as create_type,
               adminu.real_name as admin_creator,
               mtk.name         as member_creator,
               s.real_name      as creator,
               u.real_name      as updater,
               n.id,
               n.nursing_id,
               n.project_id,
               n.elder_id,
               n.task_type,
               n.estimated_server_time,
               n.mark           as amark,
               n.cancel_reason,
               n.status,
               n.rel_no,
               n.task_image,
               n.create_time    as acreate_time,
               n.update_time    as aupdate_time,
               n.create_by,
               n.update_by,
               n.remark,
               admint.real_name as acreator
        FROM `order` o
                 LEFT JOIN sys_user s ON o.create_by = s.id
                 LEFT JOIN sys_user u ON o.update_by = u.id
                 left join member m on o.member_id = m.id
                 left join elder e on o.elder_id = e.id
                 left join nursing_project p on o.project_id = p.id
                 left join bed bed on bed.id = e.bed_id
                 left join trading t on t.trading_order_no = o.trading_order_no
                 left join refund_record rr on rr.trading_order_no = o.trading_order_no
                 LEFT JOIN sys_user adminu ON rr.create_by = adminu.id
                 left join member mtk on mtk.id = rr.create_by
                 left join bill bill on o.trading_order_no = bill.trading_order_no
                 left join nursing_task n on n.rel_no = bill.bill_no
                 LEFT JOIN sys_user admint ON n.update_by = admint.id
        where o.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="listByMemberId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from `order`
        where member_id = #{memberId}
        and status in (1,3)
    </select>

    <select id="selectByStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from `order`
        where status = #{status,jdbcType=INTEGER}
    </select>


    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        update `order`
        set view_status = 1
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zzyl.entity.Order" useGeneratedKeys="true" keyProperty="id">
        insert into `order` (id, trading_order_no, payment_status,
                             amount, refund, is_refund,
                             member_id, project_id, elder_id,
                             estimated_arrival_time, order_no, reason,
                             status, create_time, update_time,
                             create_by, update_by, remark)
        values (#{id,jdbcType=BIGINT}, #{tradingOrderNo,jdbcType=BIGINT}, #{paymentStatus,jdbcType=TINYINT},
                #{amount,jdbcType=DECIMAL}, #{refund,jdbcType=DECIMAL}, #{isRefund,jdbcType=VARCHAR},
                #{memberId,jdbcType=BIGINT}, #{projectId,jdbcType=BIGINT}, #{elderId,jdbcType=BIGINT},
                #{estimatedArrivalTime,jdbcType=TIMESTAMP}, #{orderNo,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR},
                #{status,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
                #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{remark,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.zzyl.entity.Order">
        insert into `order`
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="tradingOrderNo != null">
                trading_order_no,
            </if>
            <if test="paymentStatus != null">
                payment_status,
            </if>
            <if test="amount != null">
                amount,
            </if>
            <if test="refund != null">
                refund,
            </if>
            <if test="isRefund != null">
                is_refund,
            </if>
            <if test="memberId != null">
                member_id,
            </if>
            <if test="projectId != null">
                project_id,
            </if>
            <if test="elderId != null">
                elder_id,
            </if>
            <if test="estimatedArrivalTime != null">
                estimated_arrival_time,
            </if>
            <if test="orderNo != null">
                order_no,
            </if>
            <if test="reason != null">
                reason,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="tradingOrderNo != null">
                #{tradingOrderNo,jdbcType=BIGINT},
            </if>
            <if test="paymentStatus != null">
                #{paymentStatus,jdbcType=TINYINT},
            </if>
            <if test="amount != null">
                #{amount,jdbcType=DECIMAL},
            </if>
            <if test="refund != null">
                #{refund,jdbcType=DECIMAL},
            </if>
            <if test="isRefund != null">
                #{isRefund,jdbcType=VARCHAR},
            </if>
            <if test="memberId != null">
                #{memberId,jdbcType=BIGINT},
            </if>
            <if test="projectId != null">
                #{projectId,jdbcType=BIGINT},
            </if>
            <if test="elderId != null">
                #{elderId,jdbcType=BIGINT},
            </if>
            <if test="estimatedArrivalTime != null">
                #{estimatedArrivalTime,jdbcType=TIMESTAMP},
            </if>
            <if test="orderNo != null">
                #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                #{reason,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zzyl.entity.Order" useGeneratedKeys="true"
            keyProperty="id">
        update `order`
        <set>
            <if test="tradingOrderNo != null">
                trading_order_no = #{tradingOrderNo,jdbcType=BIGINT},
            </if>
            <if test="paymentStatus != null">
                payment_status = #{paymentStatus,jdbcType=TINYINT},
            </if>
            <if test="amount != null">
                amount = #{amount,jdbcType=DECIMAL},
            </if>
            <if test="refund != null">
                refund = #{refund,jdbcType=DECIMAL},
            </if>
            <if test="isRefund != null">
                is_refund = #{isRefund,jdbcType=VARCHAR},
            </if>
            <if test="memberId != null">
                member_id = #{memberId,jdbcType=BIGINT},
            </if>
            <if test="projectId != null">
                project_id = #{projectId,jdbcType=BIGINT},
            </if>
            <if test="elderId != null">
                elder_id = #{elderId,jdbcType=BIGINT},
            </if>
            <if test="estimatedArrivalTime != null">
                estimated_arrival_time = #{estimatedArrivalTime,jdbcType=TIMESTAMP},
            </if>
            <if test="orderNo != null">
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="reason != null">
                reason = #{reason,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="createType != null">
                o_create_type = #{createType,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zzyl.entity.Order">
        update `order`
        set trading_order_no       = #{tradingOrderNo,jdbcType=BIGINT},
            payment_status         = #{paymentStatus,jdbcType=TINYINT},
            amount                 = #{amount,jdbcType=DECIMAL},
            refund                 = #{refund,jdbcType=DECIMAL},
            is_refund              = #{isRefund,jdbcType=VARCHAR},
            member_id              = #{memberId,jdbcType=BIGINT},
            project_id             = #{projectId,jdbcType=BIGINT},
            elder_id               = #{elderId,jdbcType=BIGINT},
            estimated_arrival_time = #{estimatedArrivalTime,jdbcType=TIMESTAMP},
            order_no               = #{orderNo,jdbcType=VARCHAR},
            reason                 = #{reason,jdbcType=VARCHAR},
            status                 = #{status,jdbcType=INTEGER},
            create_time            = #{createTime,jdbcType=TIMESTAMP},
            update_time            = #{updateTime,jdbcType=TIMESTAMP},
            create_by              = #{createBy,jdbcType=BIGINT},
            update_by              = #{updateBy,jdbcType=BIGINT},
            remark                 = #{remark,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="searchOrders" parameterType="map" resultMap="BaseResultMap">
        SELECT o.*, m.name as mname, m.phone, e.name as ename, p.name as pname ,p.image as pimage , bed.*
        , t.*
        , rr.memo as rrmemo , rr.refund_status as refund_status, rr.create_by as rrcreate_by,rr.refund_code,
        rr.refund_no, rr.refund_amount, rr.refund_msg, rr.refund_code, rr.refund_msg
        FROM `order` o

        left join member m on o.member_id = m.id
        left join elder e on o.elder_id = e.id
        left join nursing_project p on o.project_id = p.id
        left join bed bed on bed.id = e.bed_id
        left join trading t on t.trading_order_no = o.trading_order_no
        left join refund_record rr on rr.trading_order_no = o.trading_order_no
        <where>
            <if test="status != null">
                AND o.status = #{status,jdbcType=INTEGER}
            </if>
            <if test="orderNo != null">
                AND o.order_no = #{orderNo,jdbcType=VARCHAR}
            </if>
            <if test="elderlyName != null">
                AND e.name like concat('%',#{elderlyName},'%')
            </if>
            <if test="creator != null">
                AND m.name like concat('%',#{creator},'%')
            </if>
            <if test="userId != null">
                AND o.member_id = #{userId} and view_status = 0
            </if>
            <if test="startTime != null and endTime != null">
                AND o.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
        ORDER BY o.create_time desc
    </select>

    <update id="batchUpdateByTradingOrderNoSelective">
        update `order`
        <set>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            payment_status = 2
        </set>
        where trading_order_no in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectByTradingOrderNo" resultMap="BaseResultMap">
        select * from `order`
        where trading_order_no in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item,jdbcType=BIGINT}
        </foreach>
    </select>
</mapper>
