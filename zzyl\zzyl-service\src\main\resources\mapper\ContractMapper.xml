<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zzyl.mapper.ContractMapper">
    <resultMap id="BaseResultMap" type="com.zzyl.entity.Contract">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="contract_no" jdbcType="VARCHAR" property="contractNo"/>
        <result column="pdf_url" jdbcType="VARCHAR" property="pdfUrl"/>
        <result column="member_phone" jdbcType="VARCHAR" property="memberPhone"/>
        <result column="elder_name" jdbcType="VARCHAR" property="elderName"/>
        <result column="member_name" jdbcType="VARCHAR" property="memberName"/>
        <result column="elder_id" jdbcType="BIGINT" property="elderId"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="sort" jdbcType="INTEGER" property="sort"/>
        <result column="level_desc" jdbcType="VARCHAR" property="levelDesc"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="creator" property="creator"/>
        <result column="check_in_no" jdbcType="VARCHAR" property="checkInNo"/>
        <result column="sign_date" property="signDate"/>
        <result column="release_submitter" jdbcType="VARCHAR" property="releaseSubmitter"/>
        <result column="release_date" property="releaseDate"/>
        <result column="release_pdf_url" jdbcType="VARCHAR" property="releasePdfUrl"/>

        <collection property="elderVo" ofType="com.zzyl.vo.retreat.ElderVo">
            <id column="id" jdbcType="BIGINT" property="id"/>
            <result column="ename" jdbcType="VARCHAR" property="name"/>
            <result column="image" jdbcType="VARCHAR" property="image"/>
            <result column="id_card_no" jdbcType="VARCHAR" property="idCardNo"/>
            <result column="status" jdbcType="INTEGER" property="status"/>
            <result property="createTime" column="create_time"/>
            <result property="updateTime" column="update_time"/>
            <result column="create_by" jdbcType="BIGINT" property="createBy"/>
            <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
            <result column="remark" jdbcType="VARCHAR" property="remark"/>
        </collection>
        <collection property="roomVo" ofType="com.zzyl.vo.RoomVo">
            <id property="id" column="id"/>
            <result property="code" column="code"/>
            <result property="sort" column="sort"/>
            <result property="sort" column="sort"/>
            <result property="floorId" column="floor_id"/>
            <result column="create_by" property="createBy"/>
            <result column="update_by" property="updateBy"/>
            <result column="remark" property="remark"/>
            <result column="create_time" property="createTime"/>
            <result column="update_time" property="updateTime"/>
        </collection>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , name, contract_no, pdf_url, member_phone, elder_id, start_time, end_time, status,
    sort, level_desc, create_time, create_by, update_time, update_by, remark, check_in_no,
    sign_date, release_submitter, release_date, release_pdf_url
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select c.*, e.name as ename, e.id_card_no as id_card_no, r.*, s.real_name as creator
        from contract c
                 left join elder e on c.elder_id = e.id
                 left join bed bed on bed.id = e.bed_id
                 left join room r on r.id = bed.room_id
                 LEFT JOIN sys_user s ON c.create_by = s.id
        where c.id = #{id,jdbcType=BIGINT}
    </select>

    <select id="listAllContracts" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from contract
    </select>

    <select id="listByMemberPhone" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from contract
        where member_phone = #{memberPhone}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete
        from contract
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <insert id="insert" parameterType="com.zzyl.entity.Contract">
        insert into contract (id, name, contract_no,
                              pdf_url, member_phone, member_name, elder_name, elder_id,
                              start_time, end_time, status,
                              sort, level_desc, create_time,
                              create_by, update_time, update_by,
                              remark, check_in_no, sign_date,
                              release_submitter, release_date, release_pdf_url)
        values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{contractNo,jdbcType=VARCHAR},
                #{pdfUrl,jdbcType=VARCHAR}, #{memberPhone,jdbcType=BIGINT}, #{memberName,jdbcType=BIGINT},
                #{elderName,jdbcType=BIGINT}, #{elderId,jdbcType=BIGINT},
                #{startTime,jdbcType=TIMESTAMP}, #{endTime,jdbcType=TIMESTAMP}, #{status,jdbcType=TINYINT},
                #{sort,jdbcType=INTEGER}, #{levelDesc,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
                #{createBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR}, #{checkInNo,jdbcType=VARCHAR}, #{signDate,jdbcType=TIMESTAMP},
                #{releaseSubmitter,jdbcType=VARCHAR}, #{releaseDate,jdbcType=TIMESTAMP},
                #{releasePdfUrl,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.zzyl.entity.Contract">
        insert into contract
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="contractNo != null">
                contract_no,
            </if>
            <if test="pdfUrl != null">
                pdf_url,
            </if>
            <if test="memberPhone != null">
                member_phone,
            </if>
            <if test="elderId != null">
                elder_id,
            </if>
            <if test="startTime != null">
                start_time,
            </if>
            <if test="endTime != null">
                end_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="sort != null">
                sort,
            </if>
            <if test="levelDesc != null">
                level_desc,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="checkInNo != null">
                check_in_no,
            </if>
            <if test="signDate != null">
                sign_date,
            </if>
            <if test="releaseSubmitter != null">
                release_submitter,
            </if>
            <if test="releaseDate != null">
                release_date,
            </if>
            <if test="releasePdfUrl != null">
                release_pdf_url,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="contractNo != null">
                #{contractNo,jdbcType=VARCHAR},
            </if>
            <if test="pdfUrl != null">
                #{pdfUrl,jdbcType=VARCHAR},
            </if>
            <if test="memberPhone != null">
                #{memberPhone,jdbcType=BIGINT},
            </if>
            <if test="elderId != null">
                #{elderId,jdbcType=BIGINT},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="sort != null">
                #{sort,jdbcType=INTEGER},
            </if>
            <if test="levelDesc != null">
                #{levelDesc,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="checkInNo != null">
                #{checkInNo,jdbcType=VARCHAR},
            </if>
            <if test="signDate != null">
                #{signDate,jdbcType=TIMESTAMP},
            </if>
            <if test="releaseSubmitter != null">
                #{releaseSubmitter,jdbcType=VARCHAR},
            </if>
            <if test="releaseDate != null">
                #{releaseDate,jdbcType=TIMESTAMP},
            </if>
            <if test="releasePdfUrl != null">
                #{releasePdfUrl,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zzyl.entity.Contract">
        update contract
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="contractNo != null">
                contract_no = #{contractNo,jdbcType=VARCHAR},
            </if>
            <if test="pdfUrl != null">
                pdf_url = #{pdfUrl,jdbcType=VARCHAR},
            </if>
            <if test="memberPhone != null">
                member_phone = #{memberPhone,jdbcType=BIGINT},
            </if>
            <if test="elderId != null">
                elder_id = #{elderId,jdbcType=BIGINT},
            </if>
            <if test="startTime != null">
                start_time = #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                end_time = #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="sort != null">
                sort = #{sort,jdbcType=INTEGER},
            </if>
            <if test="levelDesc != null">
                level_desc = #{levelDesc,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="checkInNo != null">
                check_in_no = #{checkInNo,jdbcType=VARCHAR},
            </if>
            <if test="signDate != null">
                sign_date = #{signDate,jdbcType=TIMESTAMP},
            </if>
            <if test="releaseSubmitter != null">
                release_submitter = #{releaseSubmitter,jdbcType=VARCHAR},
            </if>
            <if test="releaseDate != null">
                release_date = #{releaseDate,jdbcType=TIMESTAMP},
            </if>
            <if test="releasePdfUrl != null">
                release_pdf_url = #{releasePdfUrl,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.zzyl.entity.Contract">
        update contract
        set name              = #{name,jdbcType=VARCHAR},
            contract_no       = #{contractNo,jdbcType=VARCHAR},
            pdf_url           = #{pdfUrl,jdbcType=VARCHAR},
            member_phone      = #{memberPhone,jdbcType=BIGINT},
            elder_id          = #{elderId,jdbcType=BIGINT},
            start_time        = #{startTime,jdbcType=TIMESTAMP},
            end_time          = #{endTime,jdbcType=TIMESTAMP},
            status            = #{status,jdbcType=TINYINT},
            sort              = #{sort,jdbcType=INTEGER},
            level_desc        = #{levelDesc,jdbcType=VARCHAR},
            create_time       = #{createTime,jdbcType=TIMESTAMP},
            create_by         = #{createBy,jdbcType=VARCHAR},
            update_time       = #{updateTime,jdbcType=TIMESTAMP},
            update_by         = #{updateBy,jdbcType=VARCHAR},
            remark            = #{remark,jdbcType=VARCHAR},
            check_in_no       = #{checkInNo,jdbcType=VARCHAR},
            sign_date         = #{signDate,jdbcType=TIMESTAMP},
            release_submitter = #{releaseSubmitter,jdbcType=VARCHAR},
            release_date      = #{releaseDate,jdbcType=TIMESTAMP},
            release_pdf_url   = #{releasePdfUrl,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="batchUpdateByPrimaryKeySelective">
        update contract
        <set>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
        </set>
        where id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>


    <select id="selectByPage" parameterType="map" resultMap="BaseResultMap">
        select c.*, e.name as ename, e.id_card_no as id_card_no, r.*, s.real_name as creator,
        t.photo as image
        from contract c
        left join elder e on c.elder_id = e.id
        left join bed bed on bed.id = e.bed_id
        left join room r on r.id = bed.room_id
        LEFT JOIN sys_user s ON c.create_by = s.id
        left join room_type t on t.name = r.type_name
        <where>
            <if test="contractNo != null and contractNo != ''">
                and c.contract_no = #{contractNo,jdbcType=VARCHAR}
            </if>
            <if test="elderName != null and elderName != ''">
                and c.elder_name like concat('%',#{elderName},'%')
            </if>
            <if test="status != null">
                and c.status = #{status,jdbcType=TINYINT}
            </if>
            <if test="startTime != null ">
                and c.start_time &gt;= #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null ">
                and c.end_time &lt;= #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="memberPhone != null and memberPhone != ''">
                AND c.member_phone = #{memberPhone} or e.phone = #{memberPhone}
            </if>
        </where>
        order by c.create_time desc
    </select>


</mapper>
