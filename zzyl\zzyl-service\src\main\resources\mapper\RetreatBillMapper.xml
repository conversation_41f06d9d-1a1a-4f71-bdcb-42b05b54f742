<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zzyl.mapper.RetreatBillMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.zzyl.entity.RetreatBill">
        <id column="id" property="id"/>
        <result column="bill_json" property="billJson"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="elder_id" property="elderId"/>
        <result column="is_refund" property="isRefund"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="refund_voucher_url" property="refundVoucherUrl"/>
        <result column="remark" property="remark"/>
        <result column="retreat_id" property="retreatId"/>
        <result column="trading_channel" property="tradingChannel"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="BaseColumn">
        id
        , retreat_id, bill_json, refund_voucher_url, trading_channel, elder_id, is_refund, refund_amount, create_time, update_time, create_by, update_by, remark
    </sql>

    <!-- 添加记录 -->
    <insert id="insert" parameterType="com.zzyl.entity.RetreatBill">
        INSERT INTO retreat_bill
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="retreatId != null">retreat_id,</if>
            <if test="billJson != null">bill_json,</if>
            <if test="refundVoucherUrl != null">refund_voucher_url,</if>
            <if test="tradingChannel != null">trading_channel,</if>
            <if test="elderId != null">elder_id,</if>
            <if test="isRefund != null">is_refund,</if>
            <if test="refundAmount != null">refund_amount,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="retreatId != null">#{retreatId},</if>
            <if test="billJson != null">#{billJson},</if>
            <if test="refundVoucherUrl != null">#{refundVoucherUrl},</if>
            <if test="tradingChannel != null">#{tradingChannel},</if>
            <if test="elderId != null">#{elderId},</if>
            <if test="isRefund != null">#{isRefund},</if>
            <if test="refundAmount != null">#{refundAmount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <!-- 更新记录 -->
    <update id="update" parameterType="com.zzyl.entity.RetreatBill">
        UPDATE retreat_bill
        <set>
            <if test="billJson != null">bill_json = #{billJson},</if>
            <if test="refundVoucherUrl != null">refund_voucher_url = #{refundVoucherUrl},</if>
            <if test="tradingChannel != null">trading_channel = #{tradingChannel},</if>
            <if test="elderId != null">elder_id = #{elderId},</if>
            <if test="isRefund != null">is_refund = #{isRefund},</if>
            <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
        </set>
        WHERE retreat_id = #{retreatId}
    </update>


</mapper>