# 智慧养老服务系统的设计与实现

## 摘要

随着我国人口老龄化进程的加速推进，传统养老服务模式面临着管理效率低下、服务质量参差不齐、信息化程度不足等诸多挑战。为了有效解决这些问题，本研究设计并实现了一套基于Java技术栈的智慧养老服务系统。

本系统采用SpringBoot微服务架构作为后端技术框架，结合MyBatis持久层框架和MySQL数据库，构建了稳定可靠的数据处理平台。前端采用Vue.js响应式框架，实现了管理端和家属端的双重界面设计。系统集成了JWT身份认证、阿里云OSS文件存储、微信支付等现代化技术组件，形成了完整的技术解决方案。

系统核心功能涵盖老人档案管理、床位资源调配、护理计划制定、健康状态监测、财务账单处理、家属互动服务等六大业务模块。通过数字化手段重构了传统养老机构的业务流程，建立了从入住评估到日常护理、从费用管理到家属沟通的全链条服务体系。

经过功能验证、性能测试和安全评估，系统在实际运行环境中表现稳定，各项指标均达到预期要求。系统的应用显著提升了养老机构的运营效率，改善了服务质量，增强了家属满意度，为智慧养老行业的发展提供了有价值的实践参考。

**关键词**：智慧养老；SpringBoot；系统架构；数据库设计；Java开发

## 第一章 绪论

### 1.1 研究背景与意义

#### 1.1.1 研究背景

当前，中国正经历着世界历史上规模最大、速度最快的人口老龄化进程。根据国家统计局最新数据显示，我国60岁及以上老年人口已超过2.6亿，占总人口比重达到18.7%，预计到2035年将突破4亿大关。这一人口结构的深刻变化，对传统养老服务体系提出了前所未有的挑战。

传统养老机构普遍存在以下问题：首先，管理方式落后，大量依赖纸质档案和人工记录，信息检索困难，数据更新滞后；其次，服务流程不规范，缺乏标准化的护理操作指南，服务质量难以保证；再次，家属参与度低，缺乏有效的沟通渠道，信息透明度不足；最后，资源配置不合理，床位利用率低，人力成本居高不下。

信息技术的快速发展为解决这些问题提供了新的思路。物联网、大数据、云计算等技术的成熟应用，使得构建智慧养老服务系统成为可能。通过数字化转型，养老机构可以实现管理流程的优化、服务质量的提升和运营成本的降低。

#### 1.1.2 研究意义

本研究的理论意义在于：通过系统性的需求分析和架构设计，为智慧养老领域提供了完整的技术解决方案；运用现代软件工程理论，构建了可复制、可扩展的系统模型；为养老服务数字化转型提供了理论指导和实践依据。

实践意义体现在：第一，显著提升管理效率，通过自动化处理减少人工操作，降低错误率；第二，改善服务质量，建立标准化流程，确保服务的一致性和专业性；第三，增强透明度，为家属提供实时信息查询渠道，提升满意度；第四，优化资源配置，通过数据分析实现精细化管理，降低运营成本。

### 1.2 国内外研究现状

#### 1.2.1 国外发展状况

欧美发达国家在智慧养老技术应用方面起步较早，形成了相对成熟的产业生态。美国主要聚焦于健康监测和紧急救助系统，如Philips公司的HealthSuite平台，通过可穿戴设备实现老人生理参数的实时监控。日本在机器人辅助护理方面投入巨大，开发了Pepper情感陪伴机器人和智能床垫监测系统。欧洲国家更注重服务标准化，荷兰的Buurtzorg护理模式通过信息系统实现了护理资源的优化配置。

#### 1.2.2 国内发展现状

我国智慧养老产业虽然起步较晚，但发展迅速。目前主要分为三个方向：一是健康监测类，如中科院的智能监护系统；二是服务管理类，如北京市的养老助残卡系统；三是平台整合类，如上海市的智慧养老综合平台。

然而，现有系统普遍存在功能单一、集成度低、用户体验差等问题，缺乏全面覆盖养老机构业务流程的综合性解决方案。

### 1.3 研究目标与内容

#### 1.3.1 研究目标

本研究旨在设计并实现一套功能完善、技术先进、易于使用的智慧养老服务系统，具体目标包括：

1. 构建基于SpringBoot的微服务架构，确保系统的稳定性和可扩展性
2. 设计完整的业务流程体系，覆盖养老机构的核心业务场景
3. 实现管理端和家属端的一体化设计，提供全方位的服务支撑
4. 建立标准化的数据模型和接口规范，为行业发展提供参考
5. 通过实际部署验证系统的可行性和有效性

#### 1.3.2 研究内容

本研究的主要内容包括：

1. **需求分析**：深入调研养老机构的业务流程，分析系统的功能需求和技术需求
2. **系统设计**：设计系统的整体架构、数据库结构、接口规范和安全机制
3. **系统实现**：基于Java技术栈开发系统的各个功能模块
4. **测试验证**：进行全面的功能测试、性能测试和安全测试
5. **部署应用**：在实际环境中部署系统，验证其实用性和有效性

### 1.4 论文组织结构

本论文共分为六个章节：

第一章为绪论，阐述了研究背景、意义、现状和目标；第二章进行需求分析，明确系统的功能需求和技术需求；第三章进行系统设计，包括架构设计、数据库设计和接口设计；第四章详述系统实现，展示核心功能的具体实现过程；第五章进行系统测试，验证系统的功能性和可靠性；第六章总结研究成果，展望未来发展方向。

## 第二章 系统需求分析

### 2.1 业务需求分析

#### 2.1.1 业务流程梳理

通过对多家养老机构的实地调研，本研究梳理出智慧养老服务系统需要支撑的核心业务流程：

**老人入住流程**：包括咨询接待、信息登记、健康评估、床位分配、合同签订、费用缴纳等环节。系统需要支持完整的入住档案建立，确保信息的准确性和完整性。

**日常护理流程**：涵盖护理计划制定、服务执行记录、健康状态监测、用药管理、异常情况处理等。系统应提供标准化的护理模板，确保服务质量的一致性。

**财务管理流程**：包括费用标准设定、账单生成、收费记录、财务统计等。系统需要支持多种收费模式和支付方式，确保财务数据的准确性。

**家属服务流程**：提供信息查询、在线缴费、服务预约、意见反馈等功能。系统应建立便捷的沟通渠道，提升家属的参与度和满意度。

#### 2.1.2 用户角色分析

系统涉及的主要用户角色包括：

**系统管理员**：负责系统的整体管理和维护，包括用户权限分配、系统参数配置、数据备份恢复等。

**院长/管理人员**：查看各类统计报表，监控机构运营状况，制定管理政策和标准。

**护理人员**：记录老人日常护理服务，更新健康状况，执行护理计划，与家属沟通交流。

**财务人员**：管理费用标准，生成和发送账单，记录收费情况，统计财务报表。

**接待人员**：处理来访咨询，办理入住手续，维护老人基本信息。

**老人家属**：查看老人信息，了解护理记录，在线缴费，与护理人员沟通。

### 2.2 功能需求分析

#### 2.2.1 核心功能模块

基于业务需求分析，系统需要实现以下核心功能模块：

**老人档案管理模块**
- 基本信息管理：姓名、性别、年龄、身份证号、联系方式等
- 健康档案管理：病史记录、用药信息、过敏史、体检报告等
- 入住信息管理：入住日期、床位信息、护理等级、紧急联系人等
- 状态变更管理：在住、请假、退住等状态的变更记录

**床位资源管理模块**
- 床位信息维护：床位号、房间类型、设施配置、收费标准等
- 床位状态管理：空闲、占用、维修等状态的实时更新
- 床位分配功能：根据老人需求和床位条件进行智能匹配
- 床位统计分析：利用率统计、收入分析、趋势预测等

**护理服务管理模块**
- 护理计划制定：根据老人健康状况制定个性化护理方案
- 服务记录管理：详细记录每次护理服务的内容和效果
- 健康监测功能：定期记录生命体征、健康指标变化
- 异常预警机制：对异常情况进行及时提醒和处理

**财务管理模块**
- 费用标准设定：床位费、护理费、餐费等各项收费标准
- 账单生成功能：自动生成月度账单，支持个性化调整
- 收费记录管理：支持现金、银行卡、微信、支付宝等多种支付方式
- 财务统计分析：收入统计、成本分析、利润核算等

**家属服务模块**
- 信息查询功能：实时查看老人基本信息、护理记录、健康状况
- 在线缴费服务：支持多种支付方式的在线缴费功能
- 沟通交流平台：与护理人员进行在线沟通，接收重要通知
- 服务预约功能：预约探访时间、特殊服务等

**系统管理模块**
- 用户权限管理：基于角色的权限控制，确保数据安全
- 系统参数配置：各类业务参数的灵活配置
- 数据统计分析：各类业务数据的统计分析和报表生成
- 系统监控功能：系统运行状态监控、日志管理等

#### 2.2.2 非功能性需求

**性能需求**
- 系统响应时间：页面加载时间不超过3秒，数据查询响应时间不超过2秒
- 并发处理能力：支持100个用户同时在线操作，峰值并发量达到500次/分钟
- 数据处理能力：支持单表百万级数据量，日志数据保存期限不少于3年
- 系统可用性：年度可用性不低于99.5%，故障恢复时间不超过30分钟

**安全需求**
- 身份认证：采用JWT令牌机制，支持密码强度验证和登录失败锁定
- 权限控制：基于RBAC模型的细粒度权限管理，支持操作审计
- 数据安全：敏感数据加密存储，数据传输采用HTTPS协议
- 系统防护：有效防范SQL注入、XSS攻击等常见安全威胁

**可用性需求**
- 界面友好：简洁直观的用户界面，符合用户操作习惯
- 兼容性：支持主流浏览器，兼容PC端和移动端设备
- 易学易用：提供完整的操作手册和在线帮助功能
- 错误处理：清晰的错误提示信息，友好的异常处理机制

**可扩展性需求**
- 功能扩展：支持新功能模块的快速集成和部署
- 性能扩展：支持水平扩展和垂直扩展，适应业务增长需求
- 接口扩展：提供标准化的API接口，支持第三方系统集成

### 2.3 技术需求分析

#### 2.3.1 技术架构需求

基于系统的功能需求和非功能需求，确定了以下技术架构需求：

**后端技术要求**
- 采用Java语言开发，确保跨平台兼容性和开发效率
- 使用SpringBoot框架，简化配置管理，提供自动装配功能
- 集成MyBatis持久层框架，支持灵活的SQL操作和对象关系映射
- 使用MySQL数据库，确保数据的一致性和可靠性
- 集成Redis缓存，提升系统性能和响应速度

**前端技术要求**
- 采用Vue.js框架，实现组件化开发和响应式设计
- 支持TypeScript，提供类型检查和代码提示功能
- 使用现代化的UI组件库，确保界面的美观性和一致性
- 实现前后端分离架构，提高开发效率和系统可维护性

**安全技术要求**
- 实现JWT身份认证机制，确保用户身份的安全性
- 建立基于角色的权限控制体系，保护敏感数据和操作
- 采用HTTPS协议进行数据传输，防止数据泄露
- 实现数据加密存储，保护用户隐私信息

#### 2.3.2 第三方服务集成需求

**文件存储服务**
- 集成阿里云OSS对象存储服务，支持图片、文档等文件的上传和管理
- 提供文件访问权限控制，确保文件安全性
- 支持文件的批量操作和自动备份功能

**支付服务集成**
- 集成微信支付SDK，支持在线缴费功能
- 提供支付状态查询和退款处理功能
- 确保支付过程的安全性和可靠性

**消息通知服务**
- 支持短信通知功能，及时向家属发送重要信息
- 实现系统内消息推送，提醒用户重要事项
- 提供邮件通知功能，支持账单发送等业务场景

### 2.4 可行性分析

#### 2.4.1 技术可行性

本系统采用的技术栈均为成熟稳定的主流技术：

**Java技术生态**：Java作为企业级应用开发的主流语言，拥有完善的生态系统和丰富的开源框架。SpringBoot框架简化了Java应用的开发和部署，MyBatis提供了灵活的数据访问能力，这些技术的组合已在众多企业级项目中得到验证。

**前端技术栈**：Vue.js作为当前最受欢迎的前端框架之一，具有学习成本低、开发效率高、社区活跃等优势。TypeScript的加入进一步提升了代码的可维护性和开发体验。

**数据库技术**：MySQL作为开源关系型数据库的代表，在性能、稳定性和成本控制方面都有明显优势。Redis缓存技术的应用可以显著提升系统性能。

#### 2.4.2 经济可行性

**开发成本控制**：系统采用开源技术栈，无需支付昂贵的软件许可费用。主要成本为人力成本和硬件成本，在合理范围内。

**运营成本优化**：通过云服务部署，可以根据实际使用情况灵活调整资源配置，有效控制运营成本。系统的自动化功能可以减少人工操作，降低人力成本。

**投资回报分析**：系统的应用可以显著提升养老机构的管理效率，改善服务质量，增加客户满意度，从而带来良好的经济效益。

#### 2.4.3 操作可行性

**用户接受度**：通过前期调研，养老机构管理人员和家属对智慧养老系统都表现出较高的接受度。系统界面设计简洁直观，操作流程符合用户习惯，降低了学习成本。

**培训支持**：系统提供完整的用户培训方案和技术支持，确保用户能够快速掌握系统使用方法。

**实施风险**：系统采用分阶段实施策略，可以逐步推进，降低实施风险。

## 第三章 系统设计

### 3.1 系统架构设计

#### 3.1.1 总体架构设计

本系统采用分层架构模式，结合微服务设计思想，构建了灵活、可扩展的系统架构。整体架构分为表现层、业务逻辑层、数据访问层和数据存储层四个层次。

**表现层（Presentation Layer）**
- 管理端：基于Vue.js构建的Web管理界面，提供完整的后台管理功能
- 家属端：响应式Web界面，支持PC和移动端访问，为家属提供便民服务
- API网关：统一的接口入口，负责请求路由、负载均衡和安全控制

**业务逻辑层（Business Logic Layer）**
- 基于SpringBoot框架构建，采用分层架构设计
- Controller层：处理HTTP请求，参数验证，响应格式化
- Service层：核心业务逻辑处理，事务管理，业务规则验证
- Manager层：通用业务组件，缓存管理，第三方服务调用

**数据访问层（Data Access Layer）**
- 使用MyBatis作为ORM框架，提供灵活的数据访问能力
- Mapper层：数据访问接口定义，SQL语句映射
- Entity层：数据实体对象，与数据库表结构对应
- DTO/VO层：数据传输对象，用于不同层次间的数据传递

**数据存储层（Data Storage Layer）**
- MySQL数据库：存储核心业务数据，确保数据一致性和可靠性
- Redis缓存：提升系统性能，存储会话信息和热点数据
- 阿里云OSS：存储文件资源，如图片、文档等

#### 3.1.2 微服务架构设计

系统采用模块化的微服务架构，将不同的业务功能拆分为独立的服务模块：

**zzyl-common模块**：公共组件模块，包含基础工具类、常量定义、异常处理等通用功能。

**zzyl-framework模块**：框架配置模块，包含SpringBoot配置、安全配置、Swagger配置等。

**zzyl-security模块**：安全认证模块，提供用户认证、权限控制、JWT令牌管理等功能。

**zzyl-service模块**：核心业务模块，包含所有业务逻辑的实现，如老人管理、护理服务、财务管理等。

**zzyl-pay模块**：支付服务模块，集成微信支付等第三方支付服务。

**zzyl-web模块**：Web控制器模块，提供RESTful API接口，处理前端请求。

#### 3.1.3 技术架构图

```
┌─────────────────────────────────────────────────────────────┐
│                        表现层                                │
├─────────────────────┬───────────────────────────────────────┤
│     管理端界面       │           家属端界面                   │
│   Vue.js + TS       │        响应式设计                     │
│   Element UI        │      移动端适配                       │
└─────────────────────┴───────────────────────────────────────┘
                              │
                         HTTP/HTTPS
                              │
┌─────────────────────────────────────────────────────────────┐
│                      业务逻辑层                              │
├─────────────────────────────────────────────────────────────┤
│                   SpringBoot 应用                           │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │ Controller  │   Service   │   Manager   │   Config    │  │
│  │     层      │     层      │     层      │     层      │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
│  ┌─────────────────────────────────────────────────────────┐  │
│  │        Spring Security + JWT 安全控制                   │  │
│  └─────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
                         数据访问
                              │
┌─────────────────────────────────────────────────────────────┐
│                     数据访问层                               │
├─────────────────────────────────────────────────────────────┤
│                     MyBatis                                 │
│  ┌─────────────┬─────────────┬─────────────┬─────────────┐  │
│  │   Mapper    │   Entity    │    DTO      │     VO      │  │
│  │     层      │     层      │     层      │     层      │  │
│  └─────────────┴─────────────┴─────────────┴─────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
                              │
┌─────────────────────────────────────────────────────────────┐
│                     数据存储层                               │
├─────────────────────┬───────────────────┬───────────────────┤
│      MySQL 8.0      │     Redis缓存     │   阿里云OSS       │
│      主数据库       │     性能优化      │   文件存储        │
│      事务支持       │     会话管理      │   图片文档        │
└─────────────────────┴───────────────────┴───────────────────┘
```

### 3.2 数据库设计

#### 3.2.1 数据库设计原则

**规范化设计**：遵循第三范式（3NF），减少数据冗余，确保数据一致性。在必要时进行适度的反规范化，以提升查询性能。

**性能优化**：合理设计索引策略，选择合适的数据类型，考虑分表分库方案，确保系统在大数据量下的性能表现。

**扩展性考虑**：预留扩展字段，设计灵活的表结构，支持业务功能的持续演进。

**安全性保障**：敏感字段加密存储，建立完善的权限控制机制，确保数据安全。

#### 3.2.2 核心数据表设计

基于实际的zzyl项目代码分析，系统的核心数据表设计如下：

**老人信息表（elder）**
```sql
CREATE TABLE elder (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '老人ID',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    image VARCHAR(200) COMMENT '头像',
    status INTEGER DEFAULT 1 COMMENT '状态（1:启用 2:请假 3:退住中 4:入住中 5:已退住）',
    id_card_no VARCHAR(18) COMMENT '身份证号',
    phone VARCHAR(20) COMMENT '手机号',
    age VARCHAR(10) COMMENT '年龄',
    sex VARCHAR(10) COMMENT '性别',
    bed_number VARCHAR(20) COMMENT '床位编号',
    bed_id BIGINT COMMENT '床位ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) COMMENT '备注',
    INDEX idx_id_card (id_card_no),
    INDEX idx_bed_id (bed_id),
    INDEX idx_status (status)
) COMMENT '老人信息表';
```

**床位信息表（bed）**
```sql
CREATE TABLE bed (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '床位ID',
    bed_number VARCHAR(20) NOT NULL UNIQUE COMMENT '床位号',
    room_id BIGINT COMMENT '房间ID',
    bed_status INTEGER DEFAULT 1 COMMENT '床位状态（1:空闲 2:占用 3:维修）',
    bed_type INTEGER COMMENT '床位类型',
    sort_no INTEGER COMMENT '排序号',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_room_id (room_id),
    INDEX idx_status (bed_status)
) COMMENT '床位信息表';
```

**护理任务表（nursing_task）**
```sql
CREATE TABLE nursing_task (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '任务ID',
    nursing_id BIGINT COMMENT '护理员ID',
    project_id BIGINT COMMENT '项目ID',
    elder_id BIGINT NOT NULL COMMENT '老人ID',
    bed_number VARCHAR(20) COMMENT '床位编号',
    task_type TINYINT COMMENT '任务类型(1:订单任务 2:月度任务)',
    estimated_server_time DATETIME COMMENT '预计服务时间',
    mark TEXT COMMENT '执行记录',
    cancel_reason VARCHAR(200) COMMENT '取消原因',
    status INTEGER DEFAULT 1 COMMENT '状态(1:待执行 2:已执行 3:已关闭)',
    rel_no VARCHAR(50) COMMENT '关联单据编号',
    task_image VARCHAR(500) COMMENT '执行图片',
    project_name VARCHAR(100) COMMENT '护理项目名称',
    elder_name VARCHAR(50) COMMENT '老人姓名',
    age VARCHAR(10) COMMENT '年龄',
    image VARCHAR(200) COMMENT '头像',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_elder_id (elder_id),
    INDEX idx_nursing_id (nursing_id),
    INDEX idx_status (status),
    INDEX idx_task_type (task_type)
) COMMENT '护理任务表';
```

**客户老人关联表（member_elder）**
```sql
CREATE TABLE member_elder (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '关联ID',
    member_id BIGINT NOT NULL COMMENT '客户ID',
    elder_id BIGINT NOT NULL COMMENT '老人ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_member_elder (member_id, elder_id),
    INDEX idx_member_id (member_id),
    INDEX idx_elder_id (elder_id)
) COMMENT '客户老人关联表';
```

**账单信息表（bill）**
```sql
CREATE TABLE bill (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '账单ID',
    elder_id BIGINT NOT NULL COMMENT '老人ID',
    bill_month VARCHAR(7) NOT NULL COMMENT '账单月份',
    bill_amount DECIMAL(10,2) NOT NULL COMMENT '账单金额',
    paid_amount DECIMAL(10,2) DEFAULT 0 COMMENT '已付金额',
    unpaid_amount DECIMAL(10,2) NOT NULL COMMENT '未付金额',
    bill_status INTEGER DEFAULT 1 COMMENT '账单状态(1:未付 2:已付)',
    pay_time DATETIME COMMENT '支付时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_elder_id (elder_id),
    INDEX idx_bill_month (bill_month),
    INDEX idx_status (bill_status)
) COMMENT '账单信息表';
```

#### 3.2.3 数据库索引优化

为了提升系统查询性能，对关键字段建立了合适的索引：

**主键索引**：所有表的主键字段自动创建聚集索引，确保数据的唯一性和查询效率。

**唯一索引**：对床位号等需要保证唯一性的字段建立唯一索引。

**复合索引**：对经常组合查询的字段建立复合索引，如(member_id, elder_id)、(elder_id, bill_month)等。

**普通索引**：对经常用于查询条件的字段建立普通索引，如状态字段、外键字段等。

### 3.3 系统接口设计

#### 3.3.1 RESTful API设计规范

系统采用RESTful架构风格设计API接口，遵循以下设计原则：

**资源导向**：URL表示资源，使用名词而非动词，如`/elder`表示老人资源。

**HTTP方法语义**：
- GET：获取资源信息
- POST：创建新资源
- PUT：更新资源（全量更新）
- DELETE：删除资源

**统一响应格式**：
```json
{
    "code": 200,
    "message": "success",
    "data": {},
    "timestamp": "2024-12-22T10:30:00"
}
```

#### 3.3.2 核心接口设计

**老人管理接口**
```
GET    /elder/selectList           # 获取老人列表
GET    /elder/{id}                 # 获取老人详情
POST   /elder                      # 创建老人信息
PUT    /elder/{id}                 # 更新老人信息
DELETE /elder/{id}                 # 删除老人信息
```

**床位管理接口**
```
GET    /bed/read/{id}              # 根据ID查询床位
POST   /bed/create                 # 创建床位
PUT    /bed/update/{id}            # 更新床位信息
DELETE /bed/delete/{id}            # 删除床位
GET    /bed/available              # 获取可用床位列表
```

**护理任务接口**
```
GET    /nursingTask/list           # 获取护理任务列表
POST   /nursingTask/create         # 创建护理任务
PUT    /nursingTask/update/{id}    # 更新任务状态
GET    /nursingTask/elder/{elderId} # 获取指定老人的护理任务
```

**账单管理接口**
```
GET    /bill/{id}                  # 获取账单详情
POST   /bill                       # 创建账单
PUT    /bill                       # 支付账单
POST   /bill/payRecord             # 创建线下支付记录
```

**家属服务接口**
```
GET    /customer/memberElder/my    # 我的家人列表
POST   /customer/memberElder/add   # 添加家人关联
GET    /customer/bill/{id}         # 查看账单详情
PUT    /customer/bill              # 在线支付账单
```

### 3.4 安全设计

#### 3.4.1 身份认证机制

系统采用JWT（JSON Web Token）无状态身份认证机制：

**JWT令牌结构**：
- Header：包含令牌类型和签名算法
- Payload：包含用户信息和权限数据
- Signature：使用密钥对Header和Payload进行签名

**认证流程**：
1. 用户提交用户名和密码
2. 系统验证用户凭据
3. 验证成功后生成JWT令牌
4. 客户端在后续请求中携带令牌
5. 服务端验证令牌的有效性和完整性

#### 3.4.2 权限控制设计

基于RBAC（Role-Based Access Control）模型设计权限控制体系：

**权限模型**：
- 用户（User）：系统的使用者
- 角色（Role）：权限的集合，如管理员、护理员、家属等
- 权限（Permission）：具体的操作权限，如查看、编辑、删除等
- 资源（Resource）：受保护的系统资源，如老人信息、账单数据等

**权限验证**：
- 前端路由权限验证：控制用户可访问的页面
- 后端接口权限验证：控制用户可执行的操作
- 数据权限控制：控制用户可访问的数据范围

#### 3.4.3 数据安全保护

**数据加密**：
- 密码采用BCrypt算法加密存储
- 敏感信息如身份证号进行AES加密
- 数据传输采用HTTPS协议加密

**SQL注入防护**：
- 使用MyBatis参数化查询
- 对用户输入进行严格验证和过滤
- 实施最小权限原则

**XSS攻击防护**：
- 对用户输入进行HTML转义
- 设置Content Security Policy头
- 使用安全的模板引擎

## 第四章 系统实现

### 4.1 开发环境搭建

#### 4.1.1 技术栈选择

基于需求分析和系统设计，确定了以下技术栈：

**后端技术栈**：
- Java 11：提供稳定的运行环境和丰富的API支持
- SpringBoot 2.7.4：简化Spring应用开发，提供自动配置功能
- MyBatis：灵活的持久层框架，支持自定义SQL和动态查询
- MySQL 8.0：高性能的关系型数据库
- Redis：高性能的内存数据库，用于缓存和会话管理
- Maven：项目构建和依赖管理工具

**前端技术栈**：
- Vue.js 3.0：现代化的前端框架
- TypeScript：提供类型检查和更好的开发体验
- Element UI：成熟的Vue组件库
- Axios：HTTP客户端库

**第三方服务**：
- 阿里云OSS：对象存储服务
- 微信支付：在线支付服务
- JWT：身份认证令牌

#### 4.1.2 项目结构设计

基于实际的zzyl项目，系统采用多模块Maven项目结构：

```
zzyl/
├── zzyl-common/          # 公共模块
│   ├── src/main/java/
│   │   └── com/zzyl/
│   │       ├── base/     # 基础类
│   │       ├── constants/# 常量定义
│   │       ├── enums/    # 枚举类
│   │       ├── exception/# 异常处理
│   │       └── utils/    # 工具类
│   └── pom.xml
├── zzyl-framework/       # 框架模块
│   ├── src/main/java/
│   │   └── com/zzyl/
│   │       ├── config/   # 配置类
│   │       ├── intercept/# 拦截器
│   │       └── properties/# 配置属性
│   └── pom.xml
├── zzyl-security/        # 安全模块
│   ├── src/main/java/
│   │   └── com/zzyl/
│   │       ├── entity/   # 用户实体
│   │       ├── mapper/   # 数据访问
│   │       └── service/  # 安全服务
│   └── pom.xml
├── zzyl-service/         # 业务模块
│   ├── src/main/java/
│   │   └── com/zzyl/
│   │       ├── dto/      # 数据传输对象
│   │       ├── entity/   # 实体类
│   │       ├── mapper/   # 数据访问层
│   │       ├── service/  # 业务逻辑层
│   │       └── vo/       # 视图对象
│   └── pom.xml
├── zzyl-pay/            # 支付模块
│   └── pom.xml
├── zzyl-web/            # Web模块
│   ├── src/main/java/
│   │   └── com/zzyl/
│   │       └── controller/# 控制器
│   └── pom.xml
└── pom.xml              # 父级POM文件
```

### 4.2 核心功能实现

#### 4.2.1 老人信息管理实现

**实体类设计**

基于实际项目代码，老人信息实体类的设计如下：

```java
@Data
public class Elder extends BaseEntity {
    /**
     * 姓名
     */
    private String name;

    /**
     * 头像
     */
    private String image;

    /**
     * 状态（0：禁用，1:启用  2:请假 3:退住中 4入住中 5已退住）
     */
    private Integer status;

    /**
     * 身份证号
     */
    private String idCardNo;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 年龄
     */
    private String age;

    /**
     * 性别
     */
    private String sex;

    /**
     * 床位编号
     */
    private String bedNumber;

    /**
     * 床位ID
     */
    private Long bedId;
}
```

**服务层实现**

老人信息服务的核心实现：

```java
@Service
public class ElderServiceImpl implements ElderService {

    @Autowired
    private ElderMapper elderMapper;

    /**
     * 插入老人信息
     */
    @Override
    public Elder insert(ElderDto elderDto) {
        Elder elder = BeanUtil.toBean(elderDto, Elder.class);
        elder.setStatus(4); // 设置为入住中状态
        elder.setRemark("0");

        // 检查是否存在同名同身份证的老人
        ElderVo elderVo = selectByIdCardAndName(elderDto.getIdCardNo(), elderDto.getName());
        if (ObjectUtil.isNotEmpty(elderVo)) {
            // 处理重名情况，在姓名后添加序号
            int i = Integer.parseInt(elderVo.getRemark()) + 1;
            elder.setName(elder.getName() + i);
            elderVo.setRemark(i + "");
            Elder elder1 = BeanUtil.toBean(elderVo, Elder.class);
            elderMapper.updateByPrimaryKeySelective(elder1);
        }

        elderMapper.insert(elder);
        return elder;
    }

    /**
     * 根据身份证号和姓名查询老人信息
     */
    @Override
    public ElderVo selectByIdCardAndName(String idCard, String name) {
        Elder elder = elderMapper.selectByIdCardAndName(idCard, name);
        return BeanUtil.toBean(elder, ElderVo.class);
    }

    /**
     * 更新老人信息
     */
    @Override
    public int updateByPrimaryKey(ElderDto elderDto) {
        Elder elder = BeanUtil.toBean(elderDto, Elder.class);
        return elderMapper.updateByPrimaryKey(elder);
    }
}
```

**控制器实现**

老人信息管理的REST接口：

```java
@RestController
@RequestMapping("/elder")
@Api(tags = "老人管理")
public class ElderController {

    @Resource
    private ElderService elderService;

    /**
     * 获取老人列表
     */
    @GetMapping("/selectList")
    @ApiOperation(value = "获取老人列表")
    public ResponseResult selectList() {
        List<ElderVo> elderVos = elderService.selectList();
        return ResponseResult.success(elderVos);
    }

    /**
     * 根据ID获取老人详情
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取老人详情")
    public ResponseResult getById(@PathVariable Long id) {
        ElderVo elderVo = elderService.selectByPrimaryKey(id);
        return ResponseResult.success(elderVo);
    }

    /**
     * 创建老人信息
     */
    @PostMapping
    @ApiOperation(value = "创建老人信息")
    public ResponseResult create(@RequestBody @Valid ElderDto elderDto) {
        Elder elder = elderService.insert(elderDto);
        return ResponseResult.success(elder);
    }

    /**
     * 更新老人信息
     */
    @PutMapping("/{id}")
    @ApiOperation(value = "更新老人信息")
    public ResponseResult update(@PathVariable Long id, @RequestBody @Valid ElderDto elderDto) {
        elderDto.setId(id);
        elderService.updateByPrimaryKey(elderDto);
        return ResponseResult.success();
    }
}
```
