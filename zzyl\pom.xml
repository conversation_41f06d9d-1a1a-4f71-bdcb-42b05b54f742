<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zzyl</groupId>
    <artifactId>zzyl</artifactId>
    <packaging>pom</packaging>
    <version>1.0.1</version>
    <description>智慧养老项目--中州养老</description>
    <modules>
        <module>zzyl-common</module>
        <module>zzyl-framework</module>
        <module>zzyl-pay</module>
        <module>zzyl-security</module>
        <module>zzyl-service</module>
        <module>zzyl-web</module>
    </modules>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>11</java.version>
        <maven.compiler.source>11</maven.compiler.source>
        <maven.compiler.target>11</maven.compiler.target>
        <retirement.version>1.0.1</retirement.version>
        <spring-boot.version>2.7.4</spring-boot.version>
        <lombok.version>1.18.22</lombok.version>
        <hutool.version>5.8.0.M3</hutool.version>
        <!--mysql驱动版本-->
        <mysql.version>8.0.19</mysql.version>
        <mybatis.spring>2.2.0</mybatis.spring>
        <druid>1.2.1</druid>
        <commons.io.version>2.5</commons.io.version>
        <commons.fileupload.version>1.3.3</commons.fileupload.version>
        <commons.collections.version>3.2.2</commons.collections.version>
        <kaptcha.version>2.3.2</kaptcha.version>
        <fastjson.version>1.2.75</fastjson.version>
        <oshi.version>5.6.0</oshi.version>
        <!--jwt版本-->
        <jwt.version>3.8.1</jwt.version>
        <!--jjwt版本-->
        <jjwt.version>0.9.1</jjwt.version>
        <aliyun.sdk.oss>3.10.2</aliyun.sdk.oss>
        <zhyl.version>1.0.0</zhyl.version>
        <!--微信支付-->
        <wechatpay.version>0.4.7</wechatpay.version>
        <redisson.version>3.11.2</redisson.version>
        <xxl-job.version>2.3.0</xxl-job.version>
        <!--orika 拷贝工具 -->
        <orika-core.version>1.5.4</orika-core.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>
            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <!--io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>
            <!--mybatis与springboot -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis.spring}</version>
            </dependency>
            <!--druid与springboot -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid}</version>
            </dependency>
            <!--文件上传工具类 -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons.fileupload.version}</version>
            </dependency>
            <!-- 获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>
            <!-- collections工具类 -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons.collections.version}</version>
            </dependency>
            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.wechatpay-apiv3</groupId>
                <artifactId>wechatpay-apache-httpclient</artifactId>
                <version>${wechatpay.version}</version>
            </dependency>
            <!--验证码 -->
            <dependency>
                <groupId>com.github.penggle</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>
            <!--hutool工具包-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <!-- 拷贝对象 -->
            <dependency>
                <groupId>ma.glasnost.orika</groupId>
                <artifactId>orika-core</artifactId>
                <version>${orika-core.version}</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <optional>true</optional>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>3.0.3</version>
            </dependency>
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>1.3.0</version>
            </dependency>
            <!--###################- 模块管理 #######################-->
            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun.sdk.oss}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.zxing</groupId>
                <artifactId>core</artifactId>
                <version>3.3.3</version>
            </dependency>
            <!--JWT-->
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zzyl</groupId>
                <artifactId>zzyl-common</artifactId>
                <version>${retirement.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zzyl</groupId>
                <artifactId>zzyl-framework</artifactId>
                <version>${retirement.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zzyl</groupId>
                <artifactId>zzyl-service</artifactId>
                <version>${retirement.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zzyl</groupId>
                <artifactId>zzyl-pay</artifactId>
                <version>${retirement.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zzyl</groupId>
                <artifactId>zzyl-security</artifactId>
                <version>${retirement.version}</version>
            </dependency>
            <!-- mysql驱动依赖 -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <plugins>
            <!--jdk插件-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>11</source>
                    <target>11</target>
                </configuration>
            </plugin>

            <!--springboot的打包插件-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.0.RELEASE</version>
            </plugin>

            <!-- maven-surefire-plugin 测试包 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.4.2</version>
                <configuration>
                    <!-- 全局是否执行maven生命周期中的测试：是否跳过测试 -->
                    <skipTests>true</skipTests>
                    <!-- 解决测试中文乱码-->
                    <forkMode>once</forkMode>
                    <argLine>-Dfile.encoding=UTF-8</argLine>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>