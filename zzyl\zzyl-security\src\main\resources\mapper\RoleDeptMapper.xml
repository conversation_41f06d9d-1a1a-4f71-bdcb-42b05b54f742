<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zzyl.mapper.RoleDeptMapper">
    <resultMap id="BaseResultMap" type="com.zzyl.entity.RoleDept">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="role_id" jdbcType="BIGINT" property="roleId"/>
        <result column="dept_no" jdbcType="VARCHAR" property="deptNo"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_by" jdbcType="BIGINT" property="createBy"/>
        <result column="update_by" jdbcType="BIGINT" property="updateBy"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , role_id, dept_no, create_time, update_time, create_by, update_by
    </sql>

    <select id="selectInRoleIds" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sys_role_dept
        where role_id in
        <foreach close=")" collection="roleIds" item="roleId" open="(" separator=",">
            #{roleId}
        </foreach>
    </select>
    <delete id="deleteRoleDeptByRoleId" parameterType="java.lang.Long">
        delete
        from sys_role_dept
        where role_id = #{roleId,jdbcType=BIGINT}
    </delete>
    <delete id="deleteRoleDeptInRoleId" parameterType="java.util.List">
        delete from sys_role_dept
        where role_id in
        <foreach close=")" collection="roleIds" item="roleId" open="(" separator=",">
            #{roleId}
        </foreach>
    </delete>
    <insert id="insertSelective" parameterType="com.zzyl.entity.RoleDept">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into sys_role_dept
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roleId != null">
                role_id,
            </if>
            <if test="deptNo != null">
                dept_no,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roleId != null">
                #{roleId,jdbcType=BIGINT},
            </if>
            <if test="deptNo != null">
                #{deptNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                #{updateBy,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.zzyl.entity.RoleDept">
        update sys_role_dept
        <set>
            <if test="roleId != null">
                role_id = #{roleId,jdbcType=BIGINT},
            </if>
            <if test="deptNo != null">
                dept_no = #{deptNo,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <insert id="batchInsert" keyColumn="id" keyProperty="id" parameterType="java.util.List" useGeneratedKeys="true">
        insert into sys_role_dept (role_id, dept_no, create_time, update_time, create_by, update_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.roleId,jdbcType=BIGINT}, #{item.deptNo,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateTime,jdbcType=TIMESTAMP}, #{item.createBy,jdbcType=BIGINT}, #{item.updateBy,jdbcType=BIGINT})
        </foreach>
    </insert>
</mapper>
