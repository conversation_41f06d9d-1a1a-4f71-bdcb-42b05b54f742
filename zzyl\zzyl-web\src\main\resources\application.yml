#服务配置
server:
  #端口
  port: 9995
  #服务编码
  tomcat:
    uri-encoding: UTF-8
spring:
  application:
    name: zzyl
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  cache:
    type: redis
  servlet:
    multipart:
      enabled: true
      max-file-size: 5MB
      max-request-size: 6MB
  #数据源配置
  datasource:
    druid:
      driver-class-name: com.mysql.jdbc.Driver
      url: *********************************************************************************************************
      username: root
      password: root
  redis:
    host: 127.0.0.1
    port: 6379
    # password: kK6/fG8&pN7<
  activiti:
    #是否让activiti自动创建所有的历史表
    history-level: full
    #是否需要使用历史表,默认false不使用,而配置true是使用历史表
    db-history-used: true
    #流程自动部署，关闭，需要手动部署流程 服务启动的时候自动检查resources目录下的bpmn文件 如果为true自动部署流程
    check-process-definitions: false
    #关闭启动服务自动框架部署
    deployment-mode: never-fail
# MyBatis配置
mybatis:
  #mapper配置文件
  mapper-locations: classpath*:mapper*/*Mapper.xml
  type-aliases-package: com.zzyl.entity
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    # 驼峰下划线转换
    map-underscore-to-camel-case: true
    use-generated-keys: true
    default-statement-timeout: 60
    default-fetch-size: 100
logging:
  config: classpath:logback.xml
  level:
    org.springframework.web.socket: debug
knife4j:
  enable: true
zzyl:
  framework:
    security:
      ignore-url:
#        - /**
        - /resource/menus/**
        - /resource/myButten/**
        - /customer/**
        - /security/login
        - /security/logout
        - /doc.html
        - /*-swagger/**
        - /swagger-resources
        - /v2/api-docs
        - /webjars/**
        - /common/**
        - /ws/**
      defaule-password: 888itcast.CN764%...
    swagger:
      swagger-path: com.zzyl.controller
      title: 智慧养老服务
      description: 智慧养老
      contact-name: 黑马研究院
      contact-url: www.itheima.com
      contact-email: <EMAIL>
    jwt:
      base64-encoded-secret-key: $2a$10$PVtHnkj86mJgf6li/yron.LRx/cQAlaiZkBJ9BeogCNTryXJRT1YC
      ttl: 3600000
    oss:
      endpoint: oss-cn-beijing.aliyuncs.com
      accessKeyId: LTAI5tDQKg9F61aJhbmhqVRK
      accessKeySecret: ******************************
      bucketName: itheim
  wechat:
    appId: wxa54e0997e8b6649e
    appSecret: 9e4b693aa8cee71a9077e15dcac30b52
#  wechat:
#    appId: wxa37ad44a9d9ab836
#    appSecret: 85ef0e856bafed3eb55e279c3a48fa49
  aliyun:
    accessKeyId: LTAI5tDQKg9F61aJhbmhqVRK
    accessKeySecret: ******************************
    consumerGroupId: DEFAULT_GROUP
#    consumerGroupId: 0CP9WrqP8Q1lLaWFjli9000100
    regionId: cn-shanghai
    iotInstanceId: iot-06z00frq8umvkx2
    host: iot-06z00frq8umvkx2.amqp.iothub.aliyuncs.com

management:
  health:
    rabbit:
      enabled: false
xxl:
  job:
    admin:
      addresses: http://***************:8888/xxl-job-admin
    executor:
      appname: zzyl-dev-executor
      port: 9999