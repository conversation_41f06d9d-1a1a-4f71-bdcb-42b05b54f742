<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zzyl.mapper.RoomTypeMapper">

    <resultMap id="roomTypeMap" type="com.zzyl.entity.RoomType">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="bed_count" property="bedCount"/>
        <result column="price" property="price"/>
        <result column="introduction" property="introduction"/>
        <result column="photo" property="photo"/>
        <result column="status" property="status"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <insert id="addRoomType">
        INSERT INTO room_type(name, price, introduction, photo, status, create_by, update_by, remark, create_time,
                              update_time)
        VALUES (#{name}, #{price}, #{introduction}, #{photo}, #{status}, #{createBy}, #{updateBy}, #{remark},
                #{createTime}, #{updateTime})
    </insert>

    <delete id="removeRoomType">
        DELETE
        FROM room_type
        WHERE id = #{id}
    </delete>

    <update id="modifyRoomType">
        UPDATE room_type
        SET name         = #{name},
            price        = #{price},
            introduction = #{introduction},
            photo        = #{photo},
            status       = #{status},
            update_time  = #{updateTime},
            update_by    = #{updateBy}
        WHERE id = #{id}
    </update>

    <select id="findRoomTypeById" resultMap="roomTypeMap">
        SELECT *
        FROM room_type
        WHERE id = #{id}
    </select>

    <select id="findRoomTypeList" resultMap="roomTypeMap">
        SELECT t.*, s.real_name as creator
        FROM room_type t
                 LEFT JOIN sys_user s ON t.create_by = s.id
        order by create_time desc
    </select>

    <select id="findRoomTypeListByStatus" resultMap="roomTypeMap">
        SELECT *
        FROM room_type
        WHERE status = #{status}
    </select>

    <select id="findRoomTypeListByTypeName" resultMap="roomTypeMap">
        SELECT *
        FROM room_type
        WHERE name = #{name}
    </select>

    <update id="updateStatus">
        UPDATE room_type
        SET status      = #{status},
            update_time = #{updateTime},
            update_by   = #{updateBy}
        WHERE id = #{id}
    </update>

</mapper>
